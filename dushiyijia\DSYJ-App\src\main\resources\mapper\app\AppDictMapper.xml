<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.Dsyj.app.mapper.AppDictMapper">
    
    <resultMap type="com.Dsyj.app.domain.AppDict" id="AppDictResult">
        <id     property="dictCode"   column="dict_code"   />
        <result property="dictLabel"  column="dict_label"  />
        <result property="dictValue"  column="dict_value"  />
        <result property="dictType"   column="dict_type"   />
    </resultMap>
    
    <select id="selectDictDataByType" parameterType="string" resultMap="AppDictResult">
        select dict_code, dict_label, dict_value, dict_type
        from sys_dict_data
        where dict_type = #{dictType}
        order by dict_sort asc
    </select>
    
</mapper> 