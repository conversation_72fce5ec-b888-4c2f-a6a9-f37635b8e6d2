package com.Dsyj.common.event;

import org.springframework.context.ApplicationEvent;

/**
 * 房态变化事件
 */
public class RoomStatusChangeEvent extends ApplicationEvent {
    
    private String roomNumber;
    private String roomType;
    private String roomStatus;
    private String statusDate;
    private String operationType; // INSERT, UPDATE
    
    public RoomStatusChangeEvent(Object source, String roomNumber, String roomType, 
                               String roomStatus, String statusDate, String operationType) {
        super(source);
        this.roomNumber = roomNumber;
        this.roomType = roomType;
        this.roomStatus = roomStatus;
        this.statusDate = statusDate;
        this.operationType = operationType;
    }
    
    public String getRoomNumber() {
        return roomNumber;
    }
    
    public String getRoomType() {
        return roomType;
    }
    
    public String getRoomStatus() {
        return roomStatus;
    }
    
    public String getStatusDate() {
        return statusDate;
    }
    
    public String getOperationType() {
        return operationType;
    }
}