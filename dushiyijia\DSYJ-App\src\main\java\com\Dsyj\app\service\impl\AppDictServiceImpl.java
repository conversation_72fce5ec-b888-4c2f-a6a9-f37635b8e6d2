package com.Dsyj.app.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.Dsyj.app.domain.AppDict;
import com.Dsyj.app.mapper.AppDictMapper;
import com.Dsyj.app.service.IAppDictService;

/**
 * 字典数据表 服务实现层
 */
@Service
public class AppDictServiceImpl implements IAppDictService
{
    @Autowired
    private AppDictMapper appDictMapper;

    /**
     * 根据字典类型查询字典数据
     * 
     * @param dictType 字典类型
     * @return 字典数据集合
     */
    @Override
    public List<AppDict> selectDictDataByType(String dictType)
    {
        return appDictMapper.selectDictDataByType(dictType);
    }
} 