package com.Dsyj.app.service.impl;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.FastByteArrayOutputStream;
import com.google.code.kaptcha.Producer;
import com.Dsyj.app.service.IAppCaptchaService;
import com.Dsyj.common.constant.CacheConstants;
import com.Dsyj.common.constant.Constants;
import com.Dsyj.common.core.domain.AjaxResult;
import com.Dsyj.common.core.redis.RedisCache;
import com.Dsyj.common.exception.ServiceException;
import com.Dsyj.common.utils.uuid.IdUtils;
import com.Dsyj.common.utils.sign.Base64;

/**
 * 验证码服务实现类
 */
@Service
public class AppCaptchaServiceImpl implements IAppCaptchaService {
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Autowired
    private RedisCache redisCache;

    /**
     * 生成验证码
     */
    @Override
    public AjaxResult createCaptcha() {
        // 生成验证码
        String capText = captchaProducer.createText();
        String uuid = IdUtils.simpleUUID();
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

        redisCache.setCacheObject(verifyKey, capText, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);

        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try {
            BufferedImage image = captchaProducer.createImage(capText);
            ImageIO.write(image, "jpg", os);
        } catch (IOException e) {
            return AjaxResult.error(e.getMessage());
        }

        AjaxResult ajax = AjaxResult.success();
        ajax.put("uuid", uuid);
        ajax.put("img", Base64.encode(os.toByteArray()));
        return ajax;
    }

    /**
     * 校验验证码
     */
    @Override
    public void validate(String code, String uuid) {
        if (uuid == null || code == null) {
            throw new ServiceException("验证码参数不能为空");
        }
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);

        if (captcha == null) {
            throw new ServiceException("验证码已过期");
        }
        if (!code.equalsIgnoreCase(captcha)) {
            throw new ServiceException("验证码错误");
        }
    }
} 