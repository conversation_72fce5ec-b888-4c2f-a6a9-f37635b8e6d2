package com.Dsyj.app.service.impl;

import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import com.Dsyj.app.service.IAppLoginService;
import com.Dsyj.app.service.IAppCaptchaService;
import com.Dsyj.common.constant.CacheConstants;
import com.Dsyj.common.constant.Constants;
import com.Dsyj.common.core.domain.AjaxResult;
import com.Dsyj.common.core.domain.entity.SysUser;
import com.Dsyj.common.core.domain.model.LoginUser;
import com.Dsyj.common.core.redis.RedisCache;
import com.Dsyj.common.exception.ServiceException;
import com.Dsyj.common.exception.user.UserPasswordNotMatchException;
import com.Dsyj.common.utils.SecurityUtils;
import com.Dsyj.framework.manager.AsyncManager;
import com.Dsyj.framework.manager.factory.AsyncFactory;
import com.Dsyj.framework.security.context.AuthenticationContextHolder;
import com.Dsyj.framework.web.service.TokenService;

/**
 * 移动端登录服务实现类
 */
@Service
public class AppLoginServiceImpl implements IAppLoginService {
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IAppCaptchaService captchaService;

    /**
     * 登录验证
     */
    @Override
    public String login(String username, String password, String code, String uuid) {
        // 验证码校验
        captchaService.validate(code, uuid);

        // 登录前置校验
        loginPreCheck(username, password);

        // 用户验证
        Authentication authentication = null;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
            throw new UserPasswordNotMatchException();
        } finally {
            AuthenticationContextHolder.clearContext();
        }

        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功"));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 登录前置校验
     */
    private void loginPreCheck(String username, String password) {
        // 用户名或密码为空 错误
        if (username == null || password == null) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, "用户名或密码为空"));
            throw new ServiceException("用户名或密码为空");
        }
    }

    /**
     * 退出登录
     */
    @Override
    public void logout() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null) {
            String userName = loginUser.getUsername();
            // 删除用户缓存记录
            String token = loginUser.getToken();
            if (token != null) {
                String userKey = CacheConstants.LOGIN_TOKEN_KEY + token;
                redisCache.deleteObject(userKey);
            }
            // 记录用户退出日志
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(userName, Constants.LOGOUT, "退出成功"));
        }
    }

    /**
     * 生成验证码
     */
    @Override
    public AjaxResult createCaptcha() {
        return captchaService.createCaptcha();
    }
} 