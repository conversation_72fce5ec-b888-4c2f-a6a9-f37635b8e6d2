<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.Dsyj</groupId>
        <artifactId>dushiyijia</artifactId>
        <version>3.9.0</version>
    </parent>

    <artifactId>DSYJ-App</artifactId>

    <description>
        移动端接口模块
    </description>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 核心模块-->
        <dependency>
            <groupId>com.Dsyj</groupId>
            <artifactId>Dsyj-framework</artifactId>
        </dependency>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.Dsyj</groupId>
            <artifactId>Dsyj-common</artifactId>
        </dependency>

        <!-- 房态管理模块 -->
        <dependency>
            <groupId>com.Dsyj</groupId>
            <artifactId>Room_Status</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 验证码 -->
        <dependency>
            <groupId>com.github.penggle</groupId>
            <artifactId>kaptcha</artifactId>
            <version>2.3.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>javax.servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>